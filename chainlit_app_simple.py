"""Chainlit + LangGraph Copilot with Plotly support
====================================================
Version corrigée - Résout les problèmes d'affichage des graphiques,
améliore le streaming et la gestion des erreurs.
"""

###############################################################################
# 0. Standard library imports & environment tweaks
###############################################################################

import os
import sys
import uuid
import json
import pickle
import asyncio
import pprint
from typing import Dict, List, Any, Optional

# Make sure Python streams are unbuffered so print() shows up immediately.
# (Works only when the process is launched by `python -u`, but we do our part.)
os.environ.setdefault("PYTHONUNBUFFERED", "1")

###############################################################################
# 1. Third‑party imports  
###############################################################################

from dotenv import load_dotenv

import chainlit as cl
from chainlit.types import ThreadDict
from chainlit.data.sql_alchemy import SQLAlchemyDataLayer

from langchain_core.messages import HumanMessage, ToolMessage, AIMessage, AIMessageChunk
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

# Local modules (your original project structure)
from main_copilot import (
    create_agent,
    make_postgres_checkpointer,  # still used elsewhere
    AgentState,
    InputData,
    _merge,
    ensure_message_ids,
    remove_duplicate_messages,
    get_prompt_for_partner,
)
from sandbox_client import SandboxClient
from plot_template import apply_company_style  # Import the plot styling function

###############################################################################
# 2. Environment & data‑layer initialisation
###############################################################################

print("Attempting to load .env from CWD…")
load_dotenv(override=True)

DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_LANGGRAPH = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

@cl.data_layer
def get_data_layer():
    conninfo = (
        "postgresql+asyncpg://"
        f"{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    )
    print("[DEBUG CL DATA_LAYER] Initialising SQLAlchemyDataLayer…")
    return SQLAlchemyDataLayer(conninfo=conninfo, storage_provider=None)

###############################################################################
# 3. OAuth callback
###############################################################################

@cl.oauth_callback
def oauth_callback(
    provider_id: str,
    token: str,
    raw_user_data: Dict[str, str],
    default_user: cl.User,
) -> Optional[cl.User]:
    print(f"OAuth callback for provider: {provider_id}")
    return default_user

###############################################################################
# 4. Helpers
###############################################################################

def _parse_tool_content(content: Any) -> Dict[str, Any]:
    if isinstance(content, str):
        try: return json.loads(content)
        except json.JSONDecodeError: return {"raw_content": content, "error": "Not valid JSON"}
    if isinstance(content, dict): return content
    return {"raw_content": str(content), "error": "Unknown content type"}

def serialise_state(state: AgentState) -> dict:
    """Make AgentState printable (messages → small dicts)."""
    def _msg_to_dict(m):
        if isinstance(m, (HumanMessage, AIMessage, ToolMessage)):
            return {
                "type": m.__class__.__name__,
                "id": getattr(m, "id", None),
                "content": m.content if isinstance(m.content, str) else "<complex>",
            }
        return str(m)

    # Create a copy of the state to avoid modifying the original
    serialized = dict(state)
    
    # Handle messages specially
    if "messages" in serialized:
        serialized["messages"] = [_msg_to_dict(x) for x in serialized["messages"]]
    
    # Ensure output_image_paths is preserved
    if "output_image_paths" in serialized:
        serialized["output_image_paths"] = list(serialized["output_image_paths"])
    
    return serialized

###############################################################################
# 5. LangGraph initialisation helper 
###############################################################################

async def initialize_langgraph_components(thread_id: str, partner_name: str):
    """Create/restore checkpointer + agent + state for the given thread."""
    print(f"[DEBUG LG] Initialising components for thread_id={thread_id}")

    # 5.1  Checkpointer context manager
    checkpointer_cm = AsyncPostgresSaver.from_conn_string(DB_URI_LANGGRAPH)
    try:
        cp_instance = await checkpointer_cm.__aenter__()
        await cp_instance.setup()
    except Exception as exc:
        print(f"[ERROR LG] Checkpointer setup failed: {exc}")
        cp_instance = None

    cl.user_session.set("lg_checkpointer_cm", checkpointer_cm)
    cl.user_session.set("lg_checkpointer_instance", cp_instance)

    # 5.2  Agent
    if cp_instance:
        lg_agent = create_agent(checkpointer=cp_instance, partner=partner_name) 
        cl.user_session.set("lg_agent", lg_agent)
    else:
        cl.user_session.set("lg_agent", None)

    # 5.3  Agent state
    if cp_instance:
        cfg = RunnableConfig(configurable={"thread_id": thread_id})
        try:
            persisted = await cp_instance.aget(cfg) or {}
        except Exception:
            persisted = {}
    else:
        persisted = {}

    if persisted:
        # rebuild messages list into proper objects
        rebuilt: list[Any] = []
        for md in persisted.get("messages", []):
            if isinstance(md, (HumanMessage, AIMessage, ToolMessage)):
                rebuilt.append(md)
            elif isinstance(md, dict):
                typ = md.get("type", "").lower()
                if "human" in typ:
                    rebuilt.append(HumanMessage(**md))
                elif "ai" in typ:
                    rebuilt.append(AIMessage(**md))
                elif "tool" in typ:
                    rebuilt.append(ToolMessage(**md))
        curr_state: AgentState = {
            **persisted,
            "messages": rebuilt,
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
        }
    else:
        curr_state = {
            "messages": [],
            "remaining_steps": 25,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
            "partner_config": {},
            "summary": "",
            "id_last_summary": None,
        }

    cl.user_session.set("lg_agent_state", curr_state)
    cl.user_session.set("thread_id", thread_id)
    cl.user_session.set("langgraph_initialized_for_thread", bool(cp_instance))

###############################################################################
# 6. Chat‑lifecycle callbacks
###############################################################################

@cl.on_chat_start
async def on_chat_start():
    pn = os.getenv("DEFAULT_PARTNER", "oksigen")
    print(f"[DEBUG CL] on_chat_start: partner_name={pn}")
    cl.user_session.set("partner_name", pn)
    cl.user_session.set("langgraph_initialized_for_thread", False)
    cl.user_session.set("displayed_plot_filenames", set())  # Initialize empty set for tracking displayed plots
    await cl.Message(content=f"Agent initialisé (Partenaire {pn}). Dites‑moi …").send()

@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    tid = thread["id"]
    pn = thread.get("metadata", {}).get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
    await initialize_langgraph_components(tid, pn)
    st = cl.user_session.get("lg_agent_state", {"messages": []})
    cl.user_session.set("displayed_plot_filenames", set())  # Reset displayed plots set on resume
    await cl.Message(
        content=f"Conversation reprise (Partenaire {pn}). {len(st.get('messages', []))} messages enregistrés."
    ).send()

###############################################################################
# 7. Main message handler constants
###############################################################################

# Keys used to identify plots in tool payloads
IMAGE_KEYS = ("output_image_paths", "plots", "IMAGES tool", "IMAGES")

# Tool name mapping for better display
TOOL_DISPLAY_NAMES = {
    "get_data_description": "📊 Analyzing Data Structure",
    "generic_parser": "🔍 Parsing Data",
    "python_repl": "🐍 Executing Python Code",
    "plot_tool": "📈 Creating Visualization",
    "save_plot": "💾 Saving Plot",
}

def get_tool_display_name(tool_name: str) -> str:
    """Get a user-friendly display name for a tool."""
    return TOOL_DISPLAY_NAMES.get(tool_name, f"🔧 {tool_name}")

###############################################################################
# 8. Main message handler
###############################################################################


@cl.on_message
async def on_message(msg_event: cl.Message):
    active_thread_id = cl.context.session.thread_id
    if not active_thread_id:
        print(f"[CRITICAL CL] cl.context.session.thread_id is None in on_message!")
        await cl.Message(content="Erreur critique: Impossible d'identifier la session.").send()
        cl.user_session.set("langgraph_initialized_for_thread", False)
        return

    # Get the set of already displayed plots for this session
    displayed_plots_session = cl.user_session.get("displayed_plot_filenames", set())

    if not cl.user_session.get("langgraph_initialized_for_thread") or \
       cl.user_session.get("thread_id") != active_thread_id:
        print(f"[DEBUG CL] Initializing/Re-syncing LangGraph for thread_id: {active_thread_id}")
        partner_name_for_init = cl.user_session.get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
        await initialize_langgraph_components(active_thread_id, partner_name_for_init)

    lg_agent = cl.user_session.get("lg_agent")
    lg_agent_state: Optional[AgentState] = cl.user_session.get("lg_agent_state")
    partner_name = cl.user_session.get("partner_name")

    print(f"\n[DEBUG CL] === Turn Start for Thread: {active_thread_id}, User Msg ID: {msg_event.id} ===")

    sandbox_client: Optional[SandboxClient] = cl.user_session.get("sandbox_client")
    if not sandbox_client:
        try:
            sandbox_client = SandboxClient()
            cl.user_session.set("sandbox_client", sandbox_client)
            print("[DEBUG CL] SandboxClient instantiated.")
        except Exception as e:
            print(f"[ERROR CL] Failed to instantiate SandboxClient: {e}")
            await cl.Message(content="Erreur de configuration du client Sandbox.").send()
            return

    if not lg_agent or not lg_agent_state or not partner_name:
        await cl.Message(content="Erreur: Agent non initialisé. Veuillez rafraîchir.").send()
        print(f"[ERROR CL] Crucial LangGraph components missing for thread {active_thread_id}.")
        return

    human_message_obj = HumanMessage(content=msg_event.content, id=str(uuid.uuid4()))
    current_messages_for_input = list(lg_agent_state.get("messages", []))
    messages_for_lg_agent_input = current_messages_for_input + [human_message_obj]

    config_for_run = RunnableConfig(
        configurable={"thread_id": active_thread_id, "session_id": active_thread_id, "partner": partner_name}
    )
    
    # Create the assistant message
    assistant_final_msg = cl.Message(content="")
    
    # These are for the current turn's processing
    new_plot_elements_this_turn: List[cl.Plotly] = []
    processed_plot_filenames_this_turn = set()

    # Track tool steps and streaming state
    tool_steps: Dict[str, cl.Step] = {}
    current_step = None
    assistant_msg_sent = False
    streamed = False
    
    # For state management
    messages_from_current_run = []
    current_run_lg_agent_state_dict = None

    try:
        # Create root processing step
        async with cl.Step(name="Processing Request", type="chain") as root_step:
            root_step.input = msg_event.content
            current_step = root_step

            print("[DEBUG CL] Starting graph stream processing with messages mode")

            # Use messages mode for token streaming
            async for step in lg_agent.astream(
                {"messages": messages_for_lg_agent_input},
                config=config_for_run,
                stream_mode="messages"
            ):
                node = step[1].get("langgraph_node") if len(step) > 1 else None
                message = step[0] if len(step) > 0 else None
                
                print(f"[DEBUG STREAM] Node: {node}, Message type: {type(message)}")
                
                if isinstance(message, AIMessage):
                    # Track this message for state
                    messages_from_current_run.append(message)
                    
                    if node == "call_model" or node == "agent":
                        # Create reasoning step for AI response
                        if current_step == root_step:
                            reasoning_step = cl.Step(name="🤔 Agent Reasoning", type="llm")
                            await reasoning_step.send()
                            current_step = reasoning_step

                    # Handle tool calls
                    if hasattr(message, "tool_calls") and message.tool_calls:
                        print(f"[DEBUG TOOL] Tool calls detected: {[tc['name'] for tc in message.tool_calls]}")
                        
                        # Show tool selection step
                        tool_select_step = cl.Step(name="🛠️ Tool Selection", type="tool_selection")
                        tool_calls_content = "**Tool Calls:**\n\n"
                        for tool_call in message.tool_calls:
                            tool_name = tool_call.get("name", "Unknown Tool")
                            args = json.dumps(tool_call.get("args", {}), indent=2)
                            tool_calls_content += f"- **{get_tool_display_name(tool_name)}**\n```json\n{args}\n```\n"
                        tool_select_step.output = tool_calls_content
                        await tool_select_step.send()
                        
                        # Create individual tool steps
                        for tool_call in message.tool_calls:
                            tool_name = tool_call["name"]
                            tool_id = tool_call["id"]
                            if tool_id not in tool_steps:
                                tool_step = cl.Step(
                                    name=get_tool_display_name(tool_name),
                                    type="tool"
                                )
                                await tool_step.send()
                                tool_steps[tool_id] = tool_step
                                
                                # Show tool input
                                tool_args = tool_call.get("args", {})
                                if tool_args:
                                    tool_input = json.dumps(tool_args, indent=2)
                                    tool_step.input = tool_input[:500] + "..." if len(tool_input) > 500 else tool_input
                                    await tool_step.update()

                    # Handle content streaming
                    if message.content:
                        content_to_stream = ""
                        
                        if isinstance(message.content, str):
                            content_to_stream = message.content
                        elif isinstance(message.content, list):
                            # Handle list content (multimodal)
                            for part in message.content:
                                if isinstance(part, dict) and "text" in part:
                                    content_to_stream += part["text"]
                                elif isinstance(part, str):
                                    content_to_stream += part
                        
                        if content_to_stream:
                            # Send assistant message if not sent yet
                            if not assistant_msg_sent:
                                await assistant_final_msg.send()
                                assistant_msg_sent = True
                            
                            # Stream the content
                            await assistant_final_msg.stream_token(content_to_stream)
                            await current_step.stream_token(content_to_stream)
                            streamed = True
                            print(f"[DEBUG STREAM] Streamed content: {content_to_stream[:100]}...")

                    # Handle final node
                    if node == "final" or node == "__end__":
                        print("[DEBUG STREAM] Reached final node")
                        
                elif isinstance(message, ToolMessage):
                    # Track this message for state
                    messages_from_current_run.append(message)
                    
                    tool_call_id = message.tool_call_id
                    tool_name = getattr(message, "name", "Unknown")
                    
                    print(f"[DEBUG TOOL] Tool message received: {tool_name}, ID: {tool_call_id}")
                    
                    if tool_call_id in tool_steps:
                        tool_step = tool_steps[tool_call_id]
                        tool_content = _parse_tool_content(message.content)
                        
                        if isinstance(tool_content, dict):
                            if "error" in tool_content:
                                tool_step.output = f"❌ Error: {tool_content.get('error', 'Unknown error')}"
                                tool_step.is_error = True
                            else:
                                # Handle different tool outputs
                                output_summary = ""
                                if "result" in tool_content:
                                    result_str = str(tool_content["result"])
                                    output_summary = result_str[:200] + "..." if len(result_str) > 200 else result_str
                                elif "output" in tool_content:
                                    output_str = str(tool_content["output"])
                                    output_summary = output_str[:200] + "..." if len(output_str) > 200 else output_str
                                elif "stdout" in tool_content:
                                    stdout_str = str(tool_content["stdout"])
                                    output_summary = stdout_str[:200] + "..." if len(stdout_str) > 200 else stdout_str
                                else:
                                    json_str = json.dumps(tool_content, indent=2)
                                    output_summary = json_str[:200] + "..." if len(json_str) > 200 else json_str
                                
                                tool_step.output = f"✅ {output_summary}"
                                
                                # Handle plot generation
                                if "output_image_paths" in tool_content:
                                    await handle_plot_generation(
                                        tool_content["output_image_paths"],
                                        sandbox_client,
                                        active_thread_id,
                                        displayed_plots_session,
                                        processed_plot_filenames_this_turn,
                                        new_plot_elements_this_turn,
                                        tool_step
                                    )
                        else:
                            content_str = str(message.content)
                            tool_step.output = f"✅ {content_str[:200]}..." if len(content_str) > 200 else content_str
                        
                        await tool_step.update()
                    
                elif isinstance(message, HumanMessage):
                    # Track human messages for state
                    messages_from_current_run.append(message)
                    print(f"[DEBUG STREAM] Human message: {message.content[:50]}...")

            # Finalize root step
            root_step.output = assistant_final_msg.content

        # Handle plots if any were generated
        if new_plot_elements_this_turn:
            print(f"[DEBUG PLOT] Attaching {len(new_plot_elements_this_turn)} new plots to final message")
            if not assistant_msg_sent:
                await assistant_final_msg.send()
                assistant_msg_sent = True
            assistant_final_msg.elements = new_plot_elements_this_turn
            await assistant_final_msg.update()

        # Update the session's displayed plots set
        cl.user_session.set("displayed_plot_filenames", displayed_plots_session)

        # Handle case where no content was streamed
        if not streamed:
            if new_plot_elements_this_turn:
                assistant_final_msg.content = "Here are the visualizations based on your request:"
                print("[DEBUG STREAM] Added default message for empty response with plots")
            else:
                assistant_final_msg.content = "⚠️ No response generated."
                print("[DEBUG STREAM] No content was streamed and no plots were generated")

        # Persist new state
        if lg_agent_state is not None:
            lg_state_msgs = lg_agent_state.get("messages", []) + ensure_message_ids(messages_from_current_run)
            lg_agent_state["messages"], _ = remove_duplicate_messages(lg_state_msgs)

            # Update plot paths in state if available
            if new_plot_elements_this_turn:
                current_plot_paths = lg_agent_state.get("output_image_paths", [])
                new_plot_paths = [elem.name + ".pickle" for elem in new_plot_elements_this_turn]
                lg_agent_state["output_image_paths"] = list(set(current_plot_paths + new_plot_paths))
                print(f"[DEBUG PLOT STATE] Updated state with plots: {lg_agent_state['output_image_paths']}")

            cl.user_session.set("lg_agent_state", lg_agent_state)
            print("[DEBUG LG STATE]", pprint.pformat(serialise_state(lg_agent_state), width=100, compact=True))

    except Exception as e_stream:
        print(f"[ERROR CL STREAM] Exception during agent stream: {e_stream}")
        import traceback
        traceback.print_exc(file=sys.stdout)
        
        error_step = cl.Step(name="❌ Error", type="run")
        await error_step.send()
        error_step.output = f"Error: {str(e_stream)}"
        error_step.is_error = True
        await error_step.update()

    finally:
        # Ensure the assistant message is sent
        if not assistant_msg_sent:
            if not assistant_final_msg.content:
                assistant_final_msg.content = "Traitement terminé."
            await assistant_final_msg.send()
        else:
            await assistant_final_msg.update()

        print(f"[DEBUG CL] Processing completed for thread: {active_thread_id}")

    print(f"[DEBUG CL] === Turn End for Thread: {active_thread_id} ===\n")


async def handle_plot_generation(plot_paths, sandbox_client, active_thread_id, 
                                displayed_plots_session, processed_plot_filenames_this_turn, 
                                new_plot_elements_this_turn, tool_step):
    """Handle plot generation and processing."""
    if not plot_paths:
        return
        
    plot_step = cl.Step(name="📊 Generating Visualizations", type="tool")
    await plot_step.send()

    for plot_path in plot_paths:
        if plot_path in displayed_plots_session or plot_path in processed_plot_filenames_this_turn:
            continue

        try:
            pickle_bytes = await sandbox_client.download_plot(
                session_id=active_thread_id,
                plot_name=plot_path,
            )
            if pickle_bytes:
                fig_obj = pickle.loads(pickle_bytes)
                fig_obj = apply_company_style(fig_obj)
                plot_elem = cl.Plotly(
                    name=os.path.basename(plot_path).rsplit(".", 1)[0],
                    figure=fig_obj,
                    display="inline",
                    size="large",
                )
                new_plot_elements_this_turn.append(plot_elem)
                displayed_plots_session.add(plot_path)
                processed_plot_filenames_this_turn.add(plot_path)
                print(f"[DEBUG PLOT] Added new plot: {plot_path}")
                
                plot_step.output = f"✅ Generated {len(new_plot_elements_this_turn)} visualization(s)"
                await plot_step.update()
                
        except Exception as e:
            print(f"[ERROR PLOT] Error processing plot {plot_path}: {e}")
            plot_step.output = f"❌ Error generating visualization: {str(e)}"
            plot_step.is_error = True
            await plot_step.update()



 