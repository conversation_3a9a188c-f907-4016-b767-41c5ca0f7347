"""
Multiple solutions for LangChain Azure OpenAI connection issues
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Your working configuration
ENDPOINT = "https://ai-agent-energisme.cognitiveservices.azure.com/"
DEPLOYMENT = "gpt-4.1-nano"
API_KEY = os.getenv("AZURE_OPENAI_API_KEY")  # Replace with your actual key
API_VERSION = "2024-12-01-preview"


def solution_1_basic_langchain():
    """Solution 1: Basic LangChain setup matching your working OpenAI SDK"""
    print("🔧 Solution 1: Basic LangChain setup")
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        llm = AzureChatOpenAI(
            azure_deployment=DEPLOYMENT,
            azure_endpoint=ENDPOINT,
            api_key=API_KEY,
            api_version=API_VERSION,

        )
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def solution_2_environment_variables():
    """Solution 2: Use environment variables (Lang<PERSON>hai<PERSON>'s preferred method)"""
    print("\n🔧 Solution 2: Using environment variables")
    
    try:
        # Set environment variables that LangChain looks for
        os.environ["AZURE_OPENAI_API_KEY"] = API_KEY
        os.environ["AZURE_OPENAI_ENDPOINT"] = ENDPOINT
        os.environ["AZURE_OPENAI_API_VERSION"] = API_VERSION
        os.environ["AZURE_OPENAI_DEPLOYMENT_NAME"] = DEPLOYMENT
        
        from langchain_openai import AzureChatOpenAI
        
        # LangChain should automatically pick up these environment variables
        llm = AzureChatOpenAI(
            azure_deployment=DEPLOYMENT,
            temperature=1.0,
            max_tokens=800,
        )
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def solution_3_different_api_version():
    """Solution 3: Try different API versions"""
    print("\n🔧 Solution 3: Testing different API versions")
    
    api_versions = [
        "2024-08-01-preview",
        "2024-06-01",
        "2024-02-01",
        "2023-12-01-preview",
        "2024-12-01-preview"  # Your current version
    ]
    
    from langchain_openai import AzureChatOpenAI
    
    for version in api_versions:
        try:
            print(f"   Testing API version: {version}")
            
            llm = AzureChatOpenAI(
                azure_deployment=DEPLOYMENT,
                azure_endpoint=ENDPOINT,
                api_key=API_KEY,
                api_version=version,
                temperature=1.0,
                max_tokens=800,
                timeout=30,  # Add timeout
            )
            
            response = llm.invoke("Hello!")
            print(f"✅ Success with {version}: {response.content[:50]}...")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed with {version}: {str(e)[:80]}...")
            continue
    
    return False


def solution_4_explicit_client():
    """Solution 4: Create explicit OpenAI client for LangChain"""
    print("\n🔧 Solution 4: Using explicit OpenAI client")
    
    try:
        from openai import AzureOpenAI
        from langchain_openai import AzureChatOpenAI
        
        # Create the same client that works for you
        azure_client = AzureOpenAI(
            api_version=API_VERSION,
            azure_endpoint=ENDPOINT,
            api_key=API_KEY,
        )
        
        # Pass the client to LangChain
        llm = AzureChatOpenAI(
            azure_deployment=DEPLOYMENT,
            azure_endpoint=ENDPOINT,
            api_key=API_KEY,
            api_version=API_VERSION,
            temperature=1.0,
            max_tokens=800,
            # client=azure_client,  # Some versions support this
        )
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def solution_5_debug_parameters():
    """Solution 5: Debug with verbose parameters"""
    print("\n🔧 Solution 5: Debug with all parameters explicit")
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        # Print all parameters for debugging
        params = {
            "azure_deployment": DEPLOYMENT,
            "azure_endpoint": ENDPOINT,
            "api_key": API_KEY,
            "api_version": API_VERSION,
            "temperature": 1.0,
            "max_tokens": 800,
            "timeout": 60,
            "max_retries": 3,
            "request_timeout": 60,
        }
        
        print("   Parameters:")
        for key, value in params.items():
            if key == "api_key":
                print(f"     {key}: {'*' * 10}...{value[-4:] if value else 'None'}")
            else:
                print(f"     {key}: {value}")
        
        llm = AzureChatOpenAI(**params)
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


def solution_6_alternative_import():
    """Solution 6: Try alternative import method"""
    print("\n🔧 Solution 6: Alternative import method")
    
    try:
        from langchain.chat_models import init_chat_model
        
        llm = init_chat_model(
            "gpt-4.1-nano",  # Format: provider:model
            api_key=API_KEY,
            azure_endpoint=ENDPOINT,
            api_version=API_VERSION,
            model_provider="azure_openai",  # Explicitly specify provider

        )
        
        response = llm.invoke("I am going to Paris, what should I see?")
        print(f"✅ Success: {response.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False


def main():
    """Test all solutions"""
    print("🔬 LangChain Azure OpenAI Connection Solutions")
    print("=" * 60)
    
    if not API_KEY:
        print("❌ Error: Please set your AZURE_OPENAI_API_KEY in .env file")
        return
    
    solutions = [

        solution_6_alternative_import,
    ]
    
    for i, solution in enumerate(solutions, 1):
        try:
            if solution():
                print(f"\n🎉 Solution {i} worked! You can use this approach.")
        except ImportError as e:
            print(f"❌ Import error in solution {i}: {e}")
        except Exception as e:
            print(f"❌ Unexpected error in solution {i}: {e}")
    else:
        print("\n💥 All solutions failed. Additional debugging needed.")
        print("\n🔧 Next steps:")
        print("1. Check your LangChain version: pip show langchain-openai")
        print("2. Try updating: pip install --upgrade langchain-openai")
        print("3. Verify your deployment name in Azure Portal")
        print("4. Test with a simpler deployment name (e.g., 'gpt-4')")


if __name__ == "__main__":
    main()